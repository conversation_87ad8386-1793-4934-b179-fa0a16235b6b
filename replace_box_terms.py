#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to replace box terminology with workpiece terminology in files.
"""
import re
import os

def replace_box_terms_in_file(filepath):
    """Replace box-related terms with workpiece-related terms in a file."""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define replacement patterns
    replacements = [
        # Basic replacements
        (r'\bbox\b', 'workpiece'),
        (r'\bBox\b', 'Workpiece'),
        (r'\bBOX\b', 'WORKPIECE'),
        (r'\bBoxs\b', 'Workpieces'),
        (r'\bboxs\b', 'workpieces'),
        (r'\bBOXS\b', 'WORKPIECES'),
        
        # Specific patterns for the codebase
        (r'box_([a-zA-Z]+)', r'workpiece_\1'),
        (r'Box_([a-zA-Z]+)', r'Workpiece_\1'),
        (r'move\(box_', r'move(workpiece_'),
        (r'box-moving', 'workpiece-moving'),
        (r'box-lifting', 'workpiece-lifting'),
        (r'moving boxes', 'moving workpieces'),
        (r'lifting boxes', 'lifting workpieces'),
        (r'colored boxes', 'colored workpieces'),
        (r'match each box', 'match each workpiece'),
        (r'all boxes', 'all workpieces'),
        (r'the box you are moving', 'the workpiece you are moving'),
        (r'two boxes move', 'two workpieces move'),
        (r'collision of boxes', 'collision of workpieces'),
        (r'move two boxes', 'move two workpieces'),
        (r'one box into', 'one workpiece into'),
        (r'has one box', 'has one workpiece'),
        (r'targets and boxes', 'targets and workpieces'),
        (r'left boxes', 'left workpieces'),
        (r'lift all boxes', 'lift all workpieces'),
        (r'lift boxes', 'lift workpieces'),
        (r'lift one box', 'lift one workpiece'),
        (r'warehouse to lift boxes', 'warehouse to lift workpieces'),
        (r'divide the group of each agent to lift all the boxes', 'divide the group of each agent to lift all the workpieces'),
        (r'box\[([0-9.]+)V\]', r'workpiece[\1V]'),
        (r'Box\[([0-9.]+)V\]', r'Workpiece[\1V]'),
        (r'The boxes are identified', 'The workpieces are identified'),
        (r'other boxes in the corner', 'other workpieces in the corner'),
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write back to file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Replaced box terms in {filepath}")

def main():
    """Main function to replace terms in all relevant files."""
    files_to_process = [
        'prompt_env2.py',
        'prompt_env3.py',
        'env2_create.py',
        'env3_create.py',
        'env2-box-arrange.py',
        'env3-box-arrange.py'
    ]
    
    for filepath in files_to_process:
        if os.path.exists(filepath):
            replace_box_terms_in_file(filepath)
        else:
            print(f"File {filepath} not found, skipping...")

if __name__ == "__main__":
    main()
