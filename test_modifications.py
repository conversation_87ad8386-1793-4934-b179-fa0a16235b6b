#!/usr/bin/env python3
"""
Test script to validate the modifications made to the multi-agent framework.
"""

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        from LLM import GPT_response, get_model_for_framework
        print("✓ LLM module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import LLM module: {e}")
        return False
    
    try:
        import prompt_env2
        print("✓ prompt_env2 module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import prompt_env2 module: {e}")
        return False
    
    try:
        import prompt_env3
        print("✓ prompt_env3 module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import prompt_env3 module: {e}")
        return False
    
    try:
        import env2_create
        print("✓ env2_create module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import env2_create module: {e}")
        return False
    
    try:
        import env3_create
        print("✓ env3_create module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import env3_create module: {e}")
        return False
    
    return True

def test_model_assignment():
    """Test the model assignment system."""
    print("\nTesting model assignment system...")
    
    try:
        from LLM import get_model_for_framework
        
        # Test CMAS framework
        model = get_model_for_framework('CMAS', 'central')
        expected = 'qwen3-235b-a22b'
        if model == expected:
            print(f"✓ CMAS framework returns correct model: {model}")
        else:
            print(f"✗ CMAS framework returned {model}, expected {expected}")
            return False
        
        # Test DMAS framework
        model = get_model_for_framework('DMAS', 'local', 'gemma-3-27b')
        expected = 'gemma-3-27b'
        if model == expected:
            print(f"✓ DMAS framework returns correct model: {model}")
        else:
            print(f"✗ DMAS framework returned {model}, expected {expected}")
            return False
        
        # Test HMAS-1 central planner
        model = get_model_for_framework('HMAS-1', 'central')
        expected = 'qwen3-235b-a22b'
        if model == expected:
            print(f"✓ HMAS-1 central planner returns correct model: {model}")
        else:
            print(f"✗ HMAS-1 central planner returned {model}, expected {expected}")
            return False
        
        # Test HMAS-1 local agent
        model = get_model_for_framework('HMAS-1', 'local', 'qwen3-30b-a3b-mlx')
        expected = 'qwen3-30b-a3b-mlx'
        if model == expected:
            print(f"✓ HMAS-1 local agent returns correct model: {model}")
        else:
            print(f"✗ HMAS-1 local agent returned {model}, expected {expected}")
            return False
        
        # Test HMAS-2 central planner
        model = get_model_for_framework('HMAS-2', 'central')
        expected = 'qwen3-235b-a22b'
        if model == expected:
            print(f"✓ HMAS-2 central planner returns correct model: {model}")
        else:
            print(f"✗ HMAS-2 central planner returned {model}, expected {expected}")
            return False
        
        # Test HMAS-2 local agent
        model = get_model_for_framework('HMAS-2', 'local', 'gemma-3-27b')
        expected = 'gemma-3-27b'
        if model == expected:
            print(f"✓ HMAS-2 local agent returns correct model: {model}")
        else:
            print(f"✗ HMAS-2 local agent returned {model}, expected {expected}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing model assignment: {e}")
        return False

def test_terminology_replacement():
    """Test that box terminology has been replaced with workpiece terminology."""
    print("\nTesting terminology replacement...")
    
    # Check prompt_env2.py
    try:
        with open('prompt_env2.py', 'r') as f:
            content = f.read()
        
        if 'workpiece' in content and 'box' not in content.lower():
            print("✓ prompt_env2.py: Box terminology successfully replaced")
        elif 'workpiece' in content:
            print("⚠ prompt_env2.py: Workpiece terminology found, but some box references may remain")
        else:
            print("✗ prompt_env2.py: Box terminology not properly replaced")
            return False
    except Exception as e:
        print(f"✗ Error checking prompt_env2.py: {e}")
        return False
    
    # Check prompt_env3.py
    try:
        with open('prompt_env3.py', 'r') as f:
            content = f.read()
        
        if 'workpiece' in content and 'box' not in content.lower():
            print("✓ prompt_env3.py: Box terminology successfully replaced")
        elif 'workpiece' in content:
            print("⚠ prompt_env3.py: Workpiece terminology found, but some box references may remain")
        else:
            print("✗ prompt_env3.py: Box terminology not properly replaced")
            return False
    except Exception as e:
        print(f"✗ Error checking prompt_env3.py: {e}")
        return False
    
    return True

def main():
    """Main test function."""
    print("=" * 60)
    print("MULTI-AGENT FRAMEWORK MODIFICATION VALIDATION")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test imports
    if not test_imports():
        all_tests_passed = False
    
    # Test model assignment
    if not test_model_assignment():
        all_tests_passed = False
    
    # Test terminology replacement
    if not test_terminology_replacement():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("✓ ALL TESTS PASSED - Modifications are working correctly!")
        print("\nSummary of changes:")
        print("1. ✓ LLM configuration updated with new endpoint and API key")
        print("2. ✓ Model assignment system implemented for all frameworks")
        print("3. ✓ Box terminology replaced with workpiece terminology")
        print("4. ✓ Environment files updated to use new model system")
        print("\nFramework configurations:")
        print("- CMAS: Uses qwen3-235b-a22b model")
        print("- DMAS: Uses selectable gemma-3-27b/qwen3-30b-a3b-mlx model")
        print("- HMAS-1: Central planner uses qwen3-235b-a22b, local agents use selectable model")
        print("- HMAS-2: Central planner uses qwen3-235b-a22b, local agents use selectable model")
    else:
        print("✗ SOME TESTS FAILED - Please review the issues above")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
